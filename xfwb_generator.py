#!/usr/bin/env python3
"""
XFWB (Master Air Waybill) XML generator.
This module generates XFWB XML documents from simplified input format.
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
import re
import uuid
import datetime
import logging
import os

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, 'xfwb_generator.log')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('xfwb_generator')

class XFWBGenerator:
    def __init__(self):
        # Define XML namespaces
        self.rsm = "iata:waybill:1"
        self.ram = "iata:datamodel:3"

        # Register namespaces for proper output formatting
        ET.register_namespace('rsm', self.rsm)
        ET.register_namespace('ram', self.ram)

        # Clear any existing namespace prefixes
        for prefix in list(ET._namespace_map.keys()):
            if prefix in [self.rsm, self.ram]:
                del ET._namespace_map[prefix]

    def create_waybill_xml(self, input_text, config=None):
        """Parse input text and create XFWB XML document"""
        try:
            # Parse the waybill information from input text
            waybill_info = self._parse_waybill_info(input_text)

            # Check if we have any AWBs
            if not waybill_info.get('awbs'):
                logger.warning("No AWBs found to generate XML")
                return "<!-- No valid AWBs found to generate XML -->"

            # If we have multiple AWBs, generate a consolidated XML document
            if len(waybill_info['awbs']) > 1:
                return self._create_consolidated_xml(waybill_info, config)

            # Apply any configuration overrides for single AWB
            if config:
                waybill_info.update(config)

            # Create root element with namespaces
            root = ET.Element(f"{{{self.rsm}}}Waybill")
            # Add namespace attributes (only once)
            root.set("xmlns:rsm", self.rsm)
            root.set("xmlns:ram", self.ram)

            # Add header documents
            self._add_message_header(root, waybill_info)
            self._add_business_header(root, waybill_info)

            # Add master consignment
            self._add_master_consignment(root, waybill_info)

            # Return formatted XML
            return self._format_xml(root)
        except Exception as e:
            logger.error(f"Error creating XFWB XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating XFWB XML: {str(e)} -->"

    def _create_consolidated_xml(self, waybill_info, config=None):
        """Create a consolidated XML document for multiple AWBs"""
        try:
            logger.info(f"Generating consolidated XML for {len(waybill_info['awbs'])} AWBs")

            # Create a container for all AWB XMLs
            consolidated_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
            consolidated_xml += '<AWBCollection>\n'

            # Process each AWB
            for i, awb in enumerate(waybill_info['awbs']):
                logger.info(f"Processing AWB {i+1}/{len(waybill_info['awbs'])}: {awb['awb_number']}")

                # Create a copy of the waybill info for this AWB
                single_awb_info = waybill_info.copy()

                # Update with this specific AWB's data
                for key, value in awb.items():
                    single_awb_info[key] = value

                # Apply any configuration overrides
                if config:
                    # Only apply non-AWB specific config
                    for key, value in config.items():
                        if key not in awb:
                            single_awb_info[key] = value

                # Create root element with namespaces
                root = ET.Element(f"{{{self.rsm}}}Waybill")
                # Add namespace attributes (only once)
                root.set("xmlns:rsm", self.rsm)
                root.set("xmlns:ram", self.ram)

                # Add header documents
                self._add_message_header(root, single_awb_info)
                self._add_business_header(root, single_awb_info)

                # Add master consignment
                self._add_master_consignment(root, single_awb_info)

                # Format the XML for this AWB
                awb_xml = self._format_xml(root)

                # Remove XML declaration from individual AWBs (except the first one)
                if i > 0:
                    awb_xml = awb_xml.replace('<?xml version="1.0" encoding="UTF-8"?>\n', '')

                # Add to consolidated XML
                consolidated_xml += awb_xml + '\n'

            consolidated_xml += '</AWBCollection>'
            return consolidated_xml

        except Exception as e:
            logger.error(f"Error creating consolidated XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating consolidated XML: {str(e)} -->"

    def _parse_waybill_info(self, input_text):
        """Parse the input text to extract waybill information"""
        logger.info(f"Parsing input text: {input_text}")

        # Initialize dictionary to store waybill information with default values
        waybill_info = {
            'awbs': [],  # List to store multiple AWBs
            'issue_date': datetime.datetime.now().isoformat(),
            'consignor_name': 'LIMA FREIGHT INTERNATIONAL',
            'consignor_address': 'FREIGHT HOUSE, CARGO WAY',
            'consignor_city': 'LONDON',
            'consignor_country': 'UNITED KINGDOM',
            'consignor_country_code': 'GB',
            'consignor_postcode': 'W1B 5HQ',
            'consignee_name': 'LIMA FREIGHT AGENTS MALAWI',
            'consignee_address': 'LOGISTICS HUB, INDUSTRIAL AREA',
            'consignee_city': 'LILONGWE',
            'consignee_country': 'MALAWI',
            'consignee_country_code': 'MW',
            'forwarder_name': 'EMIRATES SKYCARGO',
            'forwarder_id': '1234567',
            'forwarder_address': 'CARGO MEGA TERMINAL, DUBAI INTERNATIONAL AIRPORT',
            'forwarder_city': 'DUBAI',
            'forwarder_country': 'UNITED ARAB EMIRATES',
            'forwarder_country_code': 'AE',
            'forwarder_postcode': 'DXB1234'
        }

        # Parse the input text
        # Format examples:
        # "706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS/HUM"
        # "706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS/COU"

        try:
            # Split by lines in case multiple entries are provided
            lines = input_text.strip().split('\n')

            # Pattern for AWB line: {prefix}-{number}{origin}{destination}/T{pieces}K{weight}MC{volume}/{description}
            awb_pattern = r'(\d{3})-(\d+)([A-Z]{3})([A-Z]{3})\/([ST])(\d+)K(\d+\.\d+)MC(\d+\.\d+)\/(.*)'

            # Process all lines to find AWBs and their Handling Instructions codes
            current_awb = None

            for i in range(len(lines)):
                line = lines[i].strip()

                # Skip empty lines
                if not line:
                    continue

                # Check if this is an AWB line
                match = re.match(awb_pattern, line)
                if match:
                    # Create a new AWB entry
                    prefix = match.group(1)
                    number = match.group(2)
                    origin = match.group(3)
                    destination = match.group(4)
                    split_code = match.group(5)  # S or T
                    pieces = int(match.group(6))
                    weight = float(match.group(7))
                    volume = float(match.group(8))
                    description = match.group(9).strip()

                    current_awb = {
                        'awb_number': f"{prefix}-{number}",
                        'origin': origin,
                        'destination': destination,
                        'pieces': pieces,
                        'weight': weight,
                        'volume': volume,
                        'description': description,
                        'split_code': split_code,
                        'special_handling_codes': []
                    }

                    waybill_info['awbs'].append(current_awb)
                    logger.info(f"Found AWB: {current_awb['awb_number']}")

                # Check if this is a Handling Instructions code line for the current AWB
                elif line.startswith('/') and current_awb is not None:
                    # This is a Handling Instructions code line
                    codes = line.strip('/').split('/')
                    current_awb['special_handling_codes'].extend([code.strip() for code in codes if code.strip()])
                    logger.info(f"Added Handling Instructions codes to AWB {current_awb['awb_number']}: {codes}")

            # If no AWBs were found, log a warning
            if not waybill_info['awbs']:
                logger.warning("No valid AWBs found in the input text")
                return waybill_info

            # Set the primary AWB (first one) as the main waybill info for backward compatibility
            primary_awb = waybill_info['awbs'][0]
            for key, value in primary_awb.items():
                waybill_info[key] = value

            logger.info(f"Parsed {len(waybill_info['awbs'])} AWBs")
            return waybill_info

        except Exception as e:
            logger.error(f"Error parsing waybill info: {str(e)}", exc_info=True)
            raise

    def _add_message_header(self, root, waybill_info):
        """Add MessageHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}MessageHeaderDocument")

        # Add AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = waybill_info.get('awb_number', '')

        # Add document name
        ET.SubElement(header, f"{{{self.ram}}}Name").text = "Master Air Waybill"

        # Add type code (740 for Master AWB)
        ET.SubElement(header, f"{{{self.ram}}}TypeCode").text = "740"

        # Add issue date/time
        ET.SubElement(header, f"{{{self.ram}}}IssueDateTime").text = waybill_info.get('issue_date', datetime.datetime.now().isoformat())

        # Add purpose code
        ET.SubElement(header, f"{{{self.ram}}}PurposeCode").text = "Creation"

        # Add version ID
        ET.SubElement(header, f"{{{self.ram}}}VersionID").text = "4.00"

        # Add sender party
        sender = ET.SubElement(header, f"{{{self.ram}}}SenderParty")
        ET.SubElement(sender, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "DWCOPS"

        # Add recipient party
        recipient = ET.SubElement(header, f"{{{self.ram}}}RecipientParty")
        ET.SubElement(recipient, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "LLWOPS"

    def _add_business_header(self, root, waybill_info):
        """Add BusinessHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}BusinessHeaderDocument")

        # Add AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = waybill_info.get('awb_number', '')

        # Add included header note
        note = ET.SubElement(header, f"{{{self.ram}}}IncludedHeaderNote")
        ET.SubElement(note, f"{{{self.ram}}}ContentCode").text = "D"
        ET.SubElement(note, f"{{{self.ram}}}Content").text = "Consolidated Shipment"

        # Add signatory carrier authentication
        auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryCarrierAuthentication")
        ET.SubElement(auth, f"{{{self.ram}}}ActualDateTime").text = waybill_info.get('issue_date', datetime.datetime.now().isoformat())
        ET.SubElement(auth, f"{{{self.ram}}}Signatory").text = "DUMMY SIGN"

        # Add issue authentication location
        loc = ET.SubElement(auth, f"{{{self.ram}}}IssueAuthenticationLocation")
        ET.SubElement(loc, f"{{{self.ram}}}Name").text = waybill_info.get('origin', '')

    def _add_master_consignment(self, root, waybill_info):
        """Add MasterConsignment to the XML"""
        consignment = ET.SubElement(root, f"{{{self.rsm}}}MasterConsignment")

        # Add charge indicators
        ET.SubElement(consignment, f"{{{self.ram}}}TotalChargePrepaidIndicator").text = "P"
        ET.SubElement(consignment, f"{{{self.ram}}}TotalDisbursementPrepaidIndicator").text = "P"

        # Add weight and volume
        ET.SubElement(consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure", unitCode="KGM").text = str(waybill_info.get('weight', 0.0))
        ET.SubElement(consignment, f"{{{self.ram}}}GrossVolumeMeasure", unitCode="MTQ").text = str(waybill_info.get('volume', 0.0))

        # Add total pieces
        ET.SubElement(consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(waybill_info.get('pieces', 0))

        # Add consignor party
        self._add_consignor_party(consignment, waybill_info)

        # Add consignee party
        self._add_consignee_party(consignment, waybill_info)

        # Add freight forwarder party
        self._add_freight_forwarder_party(consignment, waybill_info)

        # Add origin location
        origin = ET.SubElement(consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = waybill_info.get('origin', '')

        # Add destination location
        destination = ET.SubElement(consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = waybill_info.get('destination', '')

        # Add currency exchange
        currency = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableOriginCurrencyExchange")
        ET.SubElement(currency, f"{{{self.ram}}}SourceCurrencyCode").text = "USD"

        # Add logistics service charge
        charge = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableLogisticsServiceCharge")
        ET.SubElement(charge, f"{{{self.ram}}}TransportPaymentMethodCode").text = "PP"

        # Add rating
        self._add_rating(consignment, waybill_info)

        # Add handling instructions
        handling = ET.SubElement(consignment, f"{{{self.ram}}}HandlingSPHInstructions")
        ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = "DEL"

        # Add Handling Instructions codes if any
        for code in waybill_info.get('special_handling_codes', []):
            handling = ET.SubElement(consignment, f"{{{self.ram}}}HandlingSPHInstructions")
            ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = code

    def _add_consignor_party(self, consignment, waybill_info):
        """Add ConsignorParty to the XML"""
        consignor = ET.SubElement(consignment, f"{{{self.ram}}}ConsignorParty")
        ET.SubElement(consignor, f"{{{self.ram}}}Name").text = waybill_info.get('consignor_name', '')

        address = ET.SubElement(consignor, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = waybill_info.get('consignor_postcode', '')
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = waybill_info.get('consignor_address', '')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = waybill_info.get('consignor_city', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = waybill_info.get('consignor_country_code', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryName").text = waybill_info.get('consignor_country', '')

    def _add_consignee_party(self, consignment, waybill_info):
        """Add ConsigneeParty to the XML"""
        consignee = ET.SubElement(consignment, f"{{{self.ram}}}ConsigneeParty")
        ET.SubElement(consignee, f"{{{self.ram}}}Name").text = waybill_info.get('consignee_name', '')

        address = ET.SubElement(consignee, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = waybill_info.get('consignee_address', '')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = waybill_info.get('consignee_city', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = waybill_info.get('consignee_country_code', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryName").text = waybill_info.get('consignee_country', '')

    def _add_freight_forwarder_party(self, consignment, waybill_info):
        """Add FreightForwarderParty to the XML"""
        forwarder = ET.SubElement(consignment, f"{{{self.ram}}}FreightForwarderParty")
        ET.SubElement(forwarder, f"{{{self.ram}}}Name").text = waybill_info.get('forwarder_name', '')
        ET.SubElement(forwarder, f"{{{self.ram}}}CargoAgentID").text = waybill_info.get('forwarder_id', '')

        address = ET.SubElement(forwarder, f"{{{self.ram}}}FreightForwarderAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = waybill_info.get('forwarder_postcode', '')
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = waybill_info.get('forwarder_address', '')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = waybill_info.get('forwarder_city', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = waybill_info.get('forwarder_country_code', '')
        ET.SubElement(address, f"{{{self.ram}}}CountryName").text = waybill_info.get('forwarder_country', '')

        location = ET.SubElement(forwarder, f"{{{self.ram}}}SpecifiedCargoAgentLocation")
        ET.SubElement(location, f"{{{self.ram}}}ID").text = waybill_info.get('origin', '')

    def _add_rating(self, consignment, waybill_info):
        """Add ApplicableRating to the XML"""
        rating = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableRating")
        ET.SubElement(rating, f"{{{self.ram}}}TypeCode").text = "F"
        ET.SubElement(rating, f"{{{self.ram}}}TotalChargeAmount", currencyID="USD").text = "0.0"
        ET.SubElement(rating, f"{{{self.ram}}}ConsignmentItemQuantity").text = "1"

        item = ET.SubElement(rating, f"{{{self.ram}}}IncludedMasterConsignmentItem")
        ET.SubElement(item, f"{{{self.ram}}}SequenceNumeric").text = "1"
        ET.SubElement(item, f"{{{self.ram}}}GrossWeightMeasure", unitCode="KGM").text = str(waybill_info.get('weight', 0.0))
        ET.SubElement(item, f"{{{self.ram}}}GrossVolumeMeasure", unitCode="MTQ").text = str(waybill_info.get('volume', 0.0))
        ET.SubElement(item, f"{{{self.ram}}}PieceQuantity").text = str(waybill_info.get('pieces', 0))
        ET.SubElement(item, f"{{{self.ram}}}Information").text = "NDA"

        nature = ET.SubElement(item, f"{{{self.ram}}}NatureIdentificationTransportCargo")
        ET.SubElement(nature, f"{{{self.ram}}}Identification").text = waybill_info.get('description', 'GENERAL CARGO')

    def _format_xml(self, root):
        """Convert ElementTree to pretty-printed XML string"""
        try:
            # Use ElementTree's built-in pretty printing
            ET.indent(ET.ElementTree(root), space="  ")

            # Convert to string and manually fix the namespace issue
            rough_string = ET.tostring(root, 'utf-8').decode('utf-8')

            # Remove duplicate namespace declarations
            rough_string = rough_string.replace(' xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3" xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3"',
                                              ' xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3"')

            # Add XML declaration
            xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'
            return xml_declaration + rough_string
        except Exception as e:
            logger.error(f"Error formatting XML: {str(e)}", exc_info=True)
            return f"<!-- Error formatting XML: {str(e)} -->"


def generate_xfwb_from_text(input_text, config=None):
    """Generate XFWB XML from the input text format"""
    try:
        # Add default values for required fields
        if not input_text or len(input_text.strip()) < 5:
            input_text = "706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS\n/HUM"

        generator = XFWBGenerator()
        result = generator.create_waybill_xml(input_text, config)
        return result
    except Exception as e:
        import traceback
        error_msg = f"Error generating XFWB XML: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"<!-- {error_msg} -->"


if __name__ == "__main__":
    # Example input
    sample_input = "706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS\n/HUM"

    # Generate XML
    xml_output = generate_xfwb_from_text(sample_input)

    # Print or save to file
    print(xml_output)

    # Optionally save to file
    with open("generated_waybill.xml", "w", encoding="utf-8") as f:
        f.write(xml_output)
