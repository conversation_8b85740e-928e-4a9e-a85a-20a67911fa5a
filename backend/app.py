from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import sys
import os
import logging
import datetime
import uuid
import re
import json  # Used for JSON operations
from logging.handlers import RotatingFileHandler

# Add the parent directory to the path so we can import the generators
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from flight_manifest_generator import generate_xml_from_text
from xfwb_generator import generate_xfwb_from_text
from xfzb_generator import generate_xfzb_from_text
from random_data_generator import generate_random_cargo_data

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, 'app.log')

handler = RotatingFileHandler(log_file, maxBytes=10000, backupCount=3)
handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
handler.setLevel(logging.INFO)

# Define the path to the static files
static_folder = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'frontend/static'))
app = Flask(__name__)
app.logger.addHandler(handler)
app.logger.setLevel(logging.INFO)
app.logger.info('IATA XML Generator startup')
app.logger.info(f'Static folder path: {static_folder}')

# Enable CORS for all routes
CORS(app, resources={r"/*": {"origins": "*"}})

# Add CORS headers to all responses
@app.after_request
def add_cors_headers(response):
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    return response

@app.route('/api/generate-xml', methods=['POST'])
def generate_xml():
    """Generate Flight Manifest XML from the input text"""
    try:
        app.logger.info('Received Flight Manifest XML generation request')
        data = request.json
        if not data:
            app.logger.error('No JSON data received')
            return jsonify({'error': 'No JSON data received'}), 400

        input_text = data.get('input_text', '')
        app.logger.info(f'Input text length: {len(input_text)}')

        if not input_text:
            app.logger.warning('No input text provided')
            return jsonify({'error': 'No input text provided'}), 400

        # Generate Flight Manifest XML
        app.logger.info('Generating Flight Manifest XML...')

        # Parse the input text to extract AWBs and ULDs
        awbs = []
        ulds = []
        current_awb = None
        lines = input_text.split('\n')
        app.logger.info(f"Processing {len(lines)} lines of input")

        for line in lines:
            app.logger.info(f"Processing line: {line}")
            # Look for ULD lines (starting with ULD/)
            if line.startswith('ULD/'):
                app.logger.info(f"Found ULD line: {line}")
                parts = line.split('/')
                if len(parts) >= 2:
                    uld_info = parts[1].strip()
                    # Extract ULD type, number, and airline
                    uld_match = re.match(r'([A-Z]{3})(\d+)([A-Z]{2})', uld_info)
                    if uld_match:
                        uld = {
                            'type': uld_match.group(1),
                            'number': uld_match.group(2),
                            'airline': uld_match.group(3),
                            'awbs': []
                        }

                        # Extract origin if available
                        if len(parts) > 2:
                            origin_part = parts[2].strip()
                            origin_match = re.match(r'([A-Z]{3})', origin_part)
                            if origin_match:
                                uld['origin'] = origin_match.group(1)
                            else:
                                uld['origin'] = "DWC"
                        else:
                            uld['origin'] = "DWC"

                        app.logger.info(f"Adding ULD: {uld}")
                        ulds.append(uld)

                        # Associate previous AWB with this ULD if available
                        if current_awb:
                            current_awb['uld'] = len(ulds) - 1  # Index of the ULD
                            uld['awbs'].append(len(awbs) - 1)  # Index of the AWB
                    else:
                        app.logger.warning(f"Could not parse ULD from line: {line}")
            # Look for AWB lines (starting with numbers like 176-...)
            elif re.match(r'^\d{3}-\d+', line):
                app.logger.info(f"Found AWB line: {line}")
                parts = line.split('/')
                awb_part = parts[0]

                # Try to match AWB with origin and destination
                awb_match = re.match(r'(\d{3})-(\d+)([A-Z]{3})([A-Z]{3})', awb_part)
                if awb_match:
                    awb = {
                        'prefix': awb_match.group(1),
                        'number': awb_match.group(2),
                        'origin': awb_match.group(3),
                        'destination': awb_match.group(4)
                    }
                else:
                    # Try a simpler pattern without origin/destination
                    simple_match = re.match(r'(\d{3})-(\d+)', awb_part)
                    if simple_match:
                        # Extract origin and destination from the rest of the string if possible
                        origin_dest_match = re.search(r'([A-Z]{3})([A-Z]{3})', awb_part)
                        origin = origin_dest_match.group(1) if origin_dest_match else "DWC"
                        destination = origin_dest_match.group(2) if origin_dest_match else "LLW"

                        awb = {
                            'prefix': simple_match.group(1),
                            'number': simple_match.group(2),
                            'origin': origin,
                            'destination': destination
                        }
                    else:
                        app.logger.warning(f"Could not parse AWB from line: {line}")
                        continue

                # Extract weight, volume, pieces, and description
                if len(parts) > 1:
                    # Process the parts of the line after the first slash

                    # Extract weight, volume, pieces
                    weight_match = re.search(r'([STPM])(\d+)K([\d\.]+)MC([\d\.]+)', parts[1])
                    if weight_match:
                        awb['split_code'] = weight_match.group(1)
                        awb['pieces'] = weight_match.group(2)
                        awb['weight'] = weight_match.group(3)
                        awb['volume'] = weight_match.group(4)
                    else:
                        app.logger.warning(f"Could not parse weight/volume from: {parts[1]}")
                        # Set default values
                        awb['split_code'] = 'T'
                        awb['pieces'] = '1'
                        awb['weight'] = '0'
                        awb['volume'] = '0'

                    # Extract description - look for the last part after a slash
                    if len(parts) > 2:
                        # The last part is the description
                        description = parts[-1].strip()
                        if description:
                            awb['description'] = description
                        else:
                            awb['description'] = 'GENERAL CARGO'

                        # Check for special codes (anything between the weight/volume and description)
                        if len(parts) > 3:  # If there are parts between weight/volume and description
                            awb['special_codes'] = [code.strip() for code in parts[2:-1] if code.strip()]
                        else:
                            awb['special_codes'] = []
                    else:
                        # No description found, use default
                        app.logger.warning(f"No description found in line: {line}")
                        awb['description'] = 'GENERAL CARGO'
                        awb['special_codes'] = []
                else:
                    # No details found, use defaults
                    awb['split_code'] = 'T'
                    awb['pieces'] = '1'
                    awb['weight'] = '0'
                    awb['volume'] = '0'
                    awb['description'] = 'GENERAL CARGO'
                    awb['special_codes'] = []

                # Associate with the last ULD if available
                if ulds:
                    awb['uld'] = len(ulds) - 1  # Index of the ULD
                    ulds[-1]['awbs'].append(len(awbs))  # Index of the AWB (after it's added)
                else:
                    awb['uld'] = None  # Will be assigned to default ULD later

                app.logger.info(f"Adding AWB: {awb}")
                awbs.append(awb)
                current_awb = awb
            elif line.startswith('/') and awbs:
                # This is a continuation line with special codes for the previous AWB
                app.logger.info(f"Found special code line: {line}")
                codes = line.strip('/').split('/')
                if awbs[-1].get('special_codes') is None:
                    awbs[-1]['special_codes'] = []
                awbs[-1]['special_codes'].extend([code.strip() for code in codes if code.strip()])

        app.logger.info(f"Found {len(awbs)} AWBs in the input")

        # Extract flight details (default values)
        flight_number = "EK9747"
        departure_date = "09MAY0820"
        departure_airport = "DWC"
        arrival_airport = "LLW"
        aircraft = "A6-EFL"

        # Try to extract flight details from the input
        # Look for the flight line (usually starts with 1/ or similar)
        for line in input_text.split('\n'):
            # Check for flight information line (format: 1/KQ756/28APR/NBO/5Y-FFE)
            flight_line_match = re.match(r'^\d+\/([A-Z]{2}\d+)\/([^\/]+)\/([A-Z]{3})\/([A-Z0-9\-]+)', line)
            if flight_line_match:
                app.logger.info(f"Found flight line: {line}")
                flight_number = flight_line_match.group(1)
                departure_date = flight_line_match.group(2)
                departure_airport = flight_line_match.group(3)
                aircraft = flight_line_match.group(4)
                break

        # Look for arrival airport
        for line in input_text.split('\n'):
            if re.match(r'^[A-Z]{3}$', line.strip()):
                arrival_airport = line.strip()
                app.logger.info(f"Found arrival airport: {arrival_airport}")
                break

        app.logger.info(f"Flight details: {flight_number}, {departure_date}, {departure_airport}, {arrival_airport}, {aircraft}")

        # Format the departure date for XML
        try:
            # Try to parse the date in various formats
            if re.match(r'^\d{2}[A-Z]{3}$', departure_date):  # Format: 28APR
                departure_month = {"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04", "MAY": "05", "JUN": "06",
                                "JUL": "07", "AUG": "08", "SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12"}
                day = departure_date[:2]
                month = departure_month.get(departure_date[2:5], "01")
                year = "2025"  # Default to current year
                formatted_date = f"{year}-{month}-{day}T00:00:00"
            elif re.match(r'^\d{2}[A-Z]{3}\d{4}$', departure_date):  # Format: 09MAY0820
                departure_month = {"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04", "MAY": "05", "JUN": "06",
                                "JUL": "07", "AUG": "08", "SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12"}
                day = departure_date[:2]
                month = departure_month.get(departure_date[2:5], "01")
                year = "2025"  # Default to current year
                hour = departure_date[5:7]
                minute = departure_date[7:9] if len(departure_date) >= 9 else "00"
                formatted_date = f"{year}-{month}-{day}T{hour}:{minute}:00"
            else:
                # Default format
                formatted_date = "2025-05-09T08:20:00"
        except Exception as e:
            app.logger.error(f"Error formatting date: {e}")
            formatted_date = "2025-05-09T08:20:00"

        app.logger.info(f"Formatted departure date: {formatted_date}")

        # Create a simple XML with the input data
        xml_output = f"""<?xml version="1.0" encoding="utf-8"?>
<ns0:FlightManifest xmlns:ns0="iata:flightmanifest:1" xmlns:ram="iata:datamodel:3">
  <ns0:MessageHeaderDocument>
    <ram:ID>{flight_number}_{departure_date}</ram:ID>
    <ram:Name>Transport Loading Report</ram:Name>
    <ram:TypeCode listID="1001" listAgencyID="6" listVersionID="D09A">122</ram:TypeCode>
    <ram:IssueDateTime>{datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")}</ram:IssueDateTime>
    <ram:PurposeCode>Creation</ram:PurposeCode>
    <ram:VersionID>4.00</ram:VersionID>
    <ram:ConversationID>{str(uuid.uuid4())}</ram:ConversationID>
    <ram:SenderParty>
      <ram:PrimaryID>{flight_number[:2].upper() if len(flight_number) >= 2 else "XX"}FM{flight_number[:2].upper() if len(flight_number) >= 2 else "XX"}</ram:PrimaryID>
    </ram:SenderParty>
    <ram:RecipientParty>
      <ram:PrimaryID>{arrival_airport}CCXA</ram:PrimaryID>
    </ram:RecipientParty>
  </ns0:MessageHeaderDocument>
  <ns0:BusinessHeaderDocument>
    <ram:ID>{flight_number}_{departure_date}</ram:ID>
  </ns0:BusinessHeaderDocument>
  <ns0:LogisticsTransportMovement>
    <ram:StageCode>Main-Carriage</ram:StageCode>
    <ram:ModeCode listID="Recommendation 19" listAgencyID="6" listVersionID="2">4</ram:ModeCode>
    <ram:Mode>AIR TRANSPORT</ram:Mode>
    <ram:ID>{flight_number}</ram:ID>
    <ram:SequenceNumeric>1</ram:SequenceNumeric>
    <ram:TotalPieceQuantity>{sum([int(awb.get('pieces', '0')) for awb in awbs]) if awbs else 50}</ram:TotalPieceQuantity>
    <ram:UsedLogisticsTransportMeans>
      <ram:Name>{aircraft}</ram:Name>
    </ram:UsedLogisticsTransportMeans>
    <ram:DepartureEvent>
      <ram:DepartureOccurrenceDateTime>{formatted_date}</ram:DepartureOccurrenceDateTime>
      <ram:DepartureDateTimeTypeCode>S</ram:DepartureDateTimeTypeCode>
      <ram:OccurrenceDepartureLocation>
        <ram:ID>{departure_airport}</ram:ID>
        <ram:TypeCode>Airport</ram:TypeCode>
      </ram:OccurrenceDepartureLocation>
    </ram:DepartureEvent>
  </ns0:LogisticsTransportMovement>
  <ns0:ArrivalEvent>
    <ram:ArrivalOccurrenceDateTime>{formatted_date.split('T')[0]}T12:40:00</ram:ArrivalOccurrenceDateTime>
    <ram:ArrivalDateTimeTypeCode>S</ram:ArrivalDateTimeTypeCode>
    <ram:DepartureOccurrenceDateTime>{formatted_date.split('T')[0]}T23:59:00</ram:DepartureOccurrenceDateTime>
    <ram:DepartureDateTimeTypeCode>S</ram:DepartureDateTimeTypeCode>
    <ram:OccurrenceArrivalLocation>
      <ram:ID>{arrival_airport}</ram:ID>
      <ram:TypeCode>Airport</ram:TypeCode>
    </ram:OccurrenceArrivalLocation>"""

        # If no ULDs were found, create a default one
        if not ulds:
            # Use the airline code from the flight number for the ULD
            airline_code = flight_number[:2] if len(flight_number) >= 2 else "KQ"

            ulds.append({
                'type': 'PMC',
                'number': '01921',
                'airline': airline_code,
                'origin': departure_airport,
                'awbs': []
            })
            # Associate all AWBs with this ULD
            for i, awb in enumerate(awbs):
                awb['uld'] = 0  # Index of the ULD
                ulds[0]['awbs'].append(i)  # Index of the AWB

        # Generate XML for each ULD
        for uld_index, uld in enumerate(ulds):
            app.logger.info(f"Generating XML for ULD: {uld.get('type', '')}{uld.get('number', '')}{uld.get('airline', '')}")

            # Start the AssociatedTransportCargo section for this ULD
            xml_output += f"""
    <ram:AssociatedTransportCargo>
      <ram:TypeCode>ULD</ram:TypeCode>
      <ram:UtilizedUnitLoadTransportEquipment>
        <ram:ID>{uld.get('number', '01921')}</ram:ID>
        <ram:CharacteristicCode>{uld.get('type', 'PMC')}</ram:CharacteristicCode>
        <ram:OperatingParty>
          <ram:PrimaryID>{uld.get('airline', 'EK')}</ram:PrimaryID>
        </ram:OperatingParty>
      </ram:UtilizedUnitLoadTransportEquipment>"""

            # Get AWBs for this ULD
            uld_awbs = [awb for awb in awbs if awb.get('uld') == uld_index]

            # If no AWBs are associated with this ULD, add a default one
            if not uld_awbs:
                xml_output += """
      <ram:IncludedMasterConsignment>
        <ram:GrossWeightMeasure unitCode="KGM">572.50</ram:GrossWeightMeasure>
        <ram:GrossVolumeMeasure unitCode="MC">1.72</ram:GrossVolumeMeasure>
        <ram:TotalPieceQuantity>50</ram:TotalPieceQuantity>
        <ram:SummaryDescription>NETWORKING EQUI</ram:SummaryDescription>
        <ram:TransportSplitDescription>S</ram:TransportSplitDescription>
        <ram:TransportContractDocument>
          <ram:ID>176-00628110</ram:ID>
        </ram:TransportContractDocument>
        <ram:OriginLocation>
          <ram:ID>DEL</ram:ID>
        </ram:OriginLocation>
        <ram:FinalDestinationLocation>
          <ram:ID>LLW</ram:ID>
        </ram:FinalDestinationLocation>
      </ram:IncludedMasterConsignment>"""
            else:
                # Add each AWB as a separate IncludedMasterConsignment
                for awb in uld_awbs:
                    app.logger.info(f"Adding AWB to ULD {uld_index}: {awb.get('prefix', '')}-{awb.get('number', '')}")
                    xml_output += f"""
      <ram:IncludedMasterConsignment>
        <ram:GrossWeightMeasure unitCode="KGM">{awb.get('weight', '0')}</ram:GrossWeightMeasure>
        <ram:GrossVolumeMeasure unitCode="MC">{awb.get('volume', '0')}</ram:GrossVolumeMeasure>
        <ram:TotalPieceQuantity>{awb.get('pieces', '0')}</ram:TotalPieceQuantity>
        <ram:SummaryDescription>{awb.get('description', '')}</ram:SummaryDescription>
        <ram:TransportSplitDescription>{awb.get('split_code', 'T')}</ram:TransportSplitDescription>
        <ram:TransportContractDocument>
          <ram:ID>{awb.get('prefix', '')}-{awb.get('number', '')}</ram:ID>
        </ram:TransportContractDocument>
        <ram:OriginLocation>
          <ram:ID>{awb.get('origin', '')}</ram:ID>
        </ram:OriginLocation>
        <ram:FinalDestinationLocation>
          <ram:ID>{awb.get('destination', '')}</ram:ID>
        </ram:FinalDestinationLocation>"""

                    # Add Handling Instructions codes if available
                    for code in awb.get('special_codes', []):
                        if code:
                            xml_output += f"""
        <ram:HandlingSPHInstructions>
          <ram:DescriptionCode>{code}</ram:DescriptionCode>
        </ram:HandlingSPHInstructions>"""

                    xml_output += """
      </ram:IncludedMasterConsignment>"""

            # Close the AssociatedTransportCargo section for this ULD
            xml_output += """
    </ram:AssociatedTransportCargo>"""

        # Close the XML
        xml_output += """
  </ns0:ArrivalEvent>
</ns0:FlightManifest>"""

        app.logger.info(f'XML generated successfully, length: {len(xml_output)}')

        return jsonify({
            'xml': xml_output,
            'success': True
        })
    except Exception as e:
        app.logger.error(f'Error generating XML: {str(e)}', exc_info=True)
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/api/generate-xfwb', methods=['POST'])
def generate_xfwb():
    """Generate XFWB (Master Air Waybill) XML from the input text"""
    try:
        app.logger.info('Received XFWB XML generation request')
        data = request.json
        if not data:
            app.logger.error('No JSON data received')
            return jsonify({'error': 'No JSON data received'}), 400

        input_text = data.get('input_text', '')
        app.logger.info(f'Input text length: {len(input_text)}')

        if not input_text:
            app.logger.warning('No input text provided')
            return jsonify({'error': 'No input text provided'}), 400

        # Get configuration if provided
        config = data.get('config', {})

        # Generate XFWB XML
        app.logger.info('Generating XFWB XML...')
        xml_output = generate_xfwb_from_text(input_text, config)

        # Check if the output is an error message
        if xml_output.startswith('<!--'):
            error_msg = xml_output.replace('<!--', '').replace('-->', '').strip()
            app.logger.error(f'Error in XFWB generation: {error_msg}')
            return jsonify({
                'error': error_msg,
                'success': False
            }), 500

        app.logger.info(f'XFWB XML generated successfully, length: {len(xml_output)}')

        # Ensure we're returning valid JSON
        response = {
            'xml': xml_output,
            'success': True
        }

        # Check if we have multiple AWBs (indicated by AWBCollection tag)
        if '<AWBCollection>' in xml_output:
            app.logger.info('Multiple AWBs detected in the response')
            # Count the number of AWBs in the response
            awb_count = xml_output.count('<ns0:Waybill')
            response['awb_count'] = awb_count
            app.logger.info(f'Found {awb_count} AWBs in the response')

        return jsonify(response)
    except Exception as e:
        app.logger.error(f'Error generating XFWB XML: {str(e)}', exc_info=True)
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route("/api/generate-xfzb", methods=["POST"])
def generate_xfzb():
    """Generate XFZB (House Waybill) XML from the input text"""
    try:
        app.logger.info("Received XFZB XML generation request")
        data = request.json
        if not data:
            app.logger.error("No JSON data received")
            return jsonify({"error": "No JSON data received"}), 400

        input_text = data.get("input_text", "")
        app.logger.info(f"Input text length: {len(input_text)}")

        if not input_text:
            app.logger.warning("No input text provided")
            return jsonify({"error": "No input text provided"}), 400

        # Get configuration if provided
        config = data.get("config", {})

        # Generate XFZB XML
        app.logger.info("Generating XFZB XML...")
        xml_output = generate_xfzb_from_text(input_text, config)

        # Check if the output is an error message
        if xml_output.startswith("<!--"):
            error_msg = xml_output.replace("<!--", "").replace("-->", "").strip()
            app.logger.error(f"Error in XFZB generation: {error_msg}")
            return jsonify({"error": error_msg, "success": False}), 500

        app.logger.info(f"XFZB XML generated successfully, length: {len(xml_output)}")

        # Ensure we are returning valid JSON
        response = {"xml": xml_output, "success": True}

        # Check if we have multiple House AWBs
        if "<HouseWaybillCollection>" in xml_output:
            app.logger.info("Multiple House AWBs detected in the response")
            house_awb_count = xml_output.count("<rsm:HouseWaybill")
            response["house_awb_count"] = house_awb_count
            app.logger.info(f"Found {house_awb_count} House AWBs in the response")

        return jsonify(response)
    except Exception as e:
        app.logger.error(f"Error generating XFZB XML: {str(e)}", exc_info=True)
        return jsonify({"error": str(e), "success": False}), 500


@app.route('/api/sample', methods=['GET'])
def get_sample():
    """Return a sample input text"""
    app.logger.info('Sample data requested')
    sample_text = """FFM/8
1/KQ756/28APR/NBO/5Y-FFE
LLW
706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
/ELI/GEN
706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA
/COU
706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS
/COU
706-41026672DXBLLW/T1K7.0MC0.13/COURIER
/COU
APL/NIL"""

    app.logger.info('Sample data sent')
    return jsonify({
        'sample': sample_text,
        'success': True
    })

@app.route('/api/sample-xfwb', methods=['GET'])
def get_sample_xfwb():
    """Return a sample XFWB input text"""
    app.logger.info('XFWB sample data requested')
    sample_text = """706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU"""

    app.logger.info('XFWB sample data sent')
    return jsonify({
        'sample': sample_text,
        'success': True
    })

@app.route("/api/sample-xfzb", methods=["GET"])
def get_sample_xfzb():
    """Return a sample XFZB input text"""
    app.logger.info("XFZB sample data requested")
    sample_text = """SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
KQ1234560/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR"""

    app.logger.info("XFZB sample data sent")
    return jsonify({
        "sample": sample_text,
        "success": True
    })


@app.route("/api/random-data", methods=["GET"])
def get_random_data():
    """Generate random cargo data for testing"""
    try:
        app.logger.info("Random cargo data requested")
        data = generate_random_cargo_data()
        app.logger.info("Random cargo data generated successfully")
        return jsonify({
            "data": data,
            "success": True
        })
    except Exception as e:
        app.logger.error(f"Error generating random data: {str(e)}", exc_info=True)
        return jsonify({
            "error": str(e),
            "success": False
        }), 500


@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    app.logger.info('Health check requested')
    return jsonify({
        'status': 'healthy',
        'message': 'IATA XML Generator API is running'
    })

@app.route('/api/debug/generator-info', methods=['GET'])
def debug_generator_info():
    """Debug endpoint to check the generator module"""
    try:
        app.logger.info('Debug generator info requested')
        generator_path = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'flight_manifest_generator.py'))

        if not os.path.exists(generator_path):
            app.logger.error(f'Generator file not found at: {generator_path}')
            return jsonify({
                'error': f'Generator file not found at: {generator_path}',
                'success': False
            }), 404

        # Get file info
        file_size = os.path.getsize(generator_path)
        file_mtime = os.path.getmtime(generator_path)

        # Try to import the function
        import inspect
        try:
            source = inspect.getsource(generate_xml_from_text)
        except Exception as e:
            source = f"Error getting source: {str(e)}"

        return jsonify({
            'generator_path': generator_path,
            'file_exists': os.path.exists(generator_path),
            'file_size': file_size,
            'file_modified': file_mtime,
            'function_exists': callable(generate_xml_from_text),
            'function_source_preview': source[:500] + '...' if len(source) > 500 else source,
            'success': True
        })
    except Exception as e:
        app.logger.error(f'Error in debug endpoint: {str(e)}', exc_info=True)
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """Return the last 100 lines of the log file"""
    try:
        app.logger.info('Logs requested')
        if not os.path.exists(log_file):
            return jsonify({
                'error': 'Log file not found',
                'success': False
            }), 404

        # Get the last 100 lines of the log file
        with open(log_file, 'r') as f:
            lines = f.readlines()
            last_lines = lines[-100:] if len(lines) > 100 else lines

        return jsonify({
            'logs': ''.join(last_lines),
            'success': True
        })
    except Exception as e:
        app.logger.error(f'Error retrieving logs: {str(e)}', exc_info=True)
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

# Routes to serve static files
@app.route('/')
def index():
    """Serve the index.html file"""
    app.logger.info('Serving index.html')
    return send_from_directory(static_folder, 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    """Serve static files"""
    app.logger.info(f'Serving static file: {path}')
    return send_from_directory(static_folder, path)

if __name__ == '__main__':
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='IATA XML Generator API')
    parser.add_argument('--port', type=int, default=5002, help='Port to run the server on')
    args = parser.parse_args()

    app.logger.info(f'Starting server on port {args.port}')
    app.run(debug=True, host='0.0.0.0', port=args.port)
