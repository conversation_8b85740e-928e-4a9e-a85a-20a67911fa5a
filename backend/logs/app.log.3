2025-06-12 06:23:14,138 INFO: Received Flight Manifest XML generation request [in /var/www/xmlmaker/backend/app.py:54]
2025-06-12 06:23:14,139 INFO: Input text length: 396 [in /var/www/xmlmaker/backend/app.py:61]
2025-06-12 06:23:14,139 INFO: Generating Flight Manifest XML... [in /var/www/xmlmaker/backend/app.py:68]
2025-06-12 06:23:14,140 INFO: Processing 15 lines of input [in /var/www/xmlmaker/backend/app.py:75]
2025-06-12 06:23:14,140 INFO: Processing line: FFM/8 [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,140 INFO: Processing line: 1/KQ701/12JUN/DAR/5Y-FFE [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,140 INFO: Processing line: LLW [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,140 INFO: Processing line: 706-60613556NBOLLW/T3K15.0MC0.01/PHARMACEUTICALS [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,140 INFO: Found AWB line: 706-60613556NBOLLW/T3K15.0MC0.01/PHARMACEUTICALS [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,140 INFO: Adding AWB: {'prefix': '706', 'number': '60613556', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '15.0', 'volume': '0.01', 'description': 'PHARMACEUTICALS', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,141 INFO: Processing line: /PIL/HEA [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,141 INFO: Found special code line: /PIL/HEA [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,141 INFO: Processing line: 706-12345675LAXLLW/T1K50.0MC0.05/AIRCRAFT ENGINE PARTS [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,141 INFO: Found AWB line: 706-12345675LAXLLW/T1K50.0MC0.05/AIRCRAFT ENGINE PARTS [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,141 INFO: Adding AWB: {'prefix': '706', 'number': '12345675', 'origin': 'LAX', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '50.0', 'volume': '0.05', 'description': 'AIRCRAFT ENGINE PARTS', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,141 INFO: Processing line: /AXA/HEA [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,141 INFO: Found special code line: /AXA/HEA [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,141 INFO: Processing line: 706-98765432JFKLLW/T2K30.0MC0.02/ELECTRONICS [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,142 INFO: Found AWB line: 706-98765432JFKLLW/T2K30.0MC0.02/ELECTRONICS [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,142 INFO: Adding AWB: {'prefix': '706', 'number': '98765432', 'origin': 'JFK', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '30.0', 'volume': '0.02', 'description': 'ELECTRONICS', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,142 INFO: Processing line: /MAG /ELM [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,142 INFO: Found special code line: /MAG /ELM [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,142 INFO: Processing line: 706-11223344AMSLLW/T1K25.0MC0.03/FRESH CUT FLOWERS [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,142 INFO: Found AWB line: 706-11223344AMSLLW/T1K25.0MC0.03/FRESH CUT FLOWERS [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,142 INFO: Adding AWB: {'prefix': '706', 'number': '11223344', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '25.0', 'volume': '0.03', 'description': 'FRESH CUT FLOWERS', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,142 INFO: Processing line: /PER/COL [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,143 INFO: Found special code line: /PER/COL [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,143 INFO: Processing line: 706-55667788CDGLLW/T3K400.0MC0.04/INDUSTRIAL TURBINE [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,143 INFO: Found AWB line: 706-55667788CDGLLW/T3K400.0MC0.04/INDUSTRIAL TURBINE [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,143 INFO: Adding AWB: {'prefix': '706', 'number': '55667788', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '400.0', 'volume': '0.04', 'description': 'INDUSTRIAL TURBINE', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,143 INFO: Processing line: /HEA/BIG [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,143 INFO: Found special code line: /HEA/BIG [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,143 INFO: Processing line: 706-44332211FRALLW/T2K35.0MC0.01/COVID19 VACCINES [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,143 INFO: Found AWB line: 706-44332211FRALLW/T2K35.0MC0.01/COVID19 VACCINES [in /var/www/xmlmaker/backend/app.py:117]
2025-06-12 06:23:14,144 INFO: Adding AWB: {'prefix': '706', 'number': '44332211', 'origin': 'FRA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '35.0', 'volume': '0.01', 'description': 'COVID19 VACCINES', 'special_codes': [], 'uld': None} [in /var/www/xmlmaker/backend/app.py:203]
2025-06-12 06:23:14,144 INFO: Processing line: /PIL/COL/HEA [in /var/www/xmlmaker/backend/app.py:78]
2025-06-12 06:23:14,144 INFO: Found special code line: /PIL/COL/HEA [in /var/www/xmlmaker/backend/app.py:208]
2025-06-12 06:23:14,144 INFO: Found 6 AWBs in the input [in /var/www/xmlmaker/backend/app.py:214]
2025-06-12 06:23:14,144 INFO: Found flight line: 1/KQ701/12JUN/DAR/5Y-FFE [in /var/www/xmlmaker/backend/app.py:229]
2025-06-12 06:23:14,144 INFO: Found arrival airport: LLW [in /var/www/xmlmaker/backend/app.py:240]
2025-06-12 06:23:14,144 INFO: Flight details: KQ701, 12JUN, DAR, LLW, 5Y-FFE [in /var/www/xmlmaker/backend/app.py:243]
2025-06-12 06:23:14,144 INFO: Formatted departure date: 2025-06-12T00:00:00 [in /var/www/xmlmaker/backend/app.py:271]
2025-06-12 06:23:14,145 INFO: Generating XML for ULD: PMC01921KQ [in /var/www/xmlmaker/backend/app.py:342]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-60613556 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-12345675 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-98765432 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-11223344 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-55667788 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,145 INFO: Adding AWB to ULD 0: 706-44332211 [in /var/www/xmlmaker/backend/app.py:381]
2025-06-12 06:23:14,146 INFO: XML generated successfully, length: 8945 [in /var/www/xmlmaker/backend/app.py:419]
2025-06-12 10:35:23,072 INFO: Received XFWB XML generation request [in /var/www/xmlmaker/backend/app.py:436]
2025-06-12 10:35:23,072 INFO: Input text length: 102 [in /var/www/xmlmaker/backend/app.py:443]
2025-06-12 10:35:23,072 INFO: Generating XFWB XML... [in /var/www/xmlmaker/backend/app.py:453]
2025-06-12 10:35:23,074 INFO: XFWB XML generated successfully, length: 9452 [in /var/www/xmlmaker/backend/app.py:465]
2025-06-12 10:35:23,074 INFO: Multiple AWBs detected in the response [in /var/www/xmlmaker/backend/app.py:475]
2025-06-12 10:35:23,074 INFO: Found 2 AWBs in the response [in /var/www/xmlmaker/backend/app.py:479]
2025-06-12 10:37:02,034 INFO: Serving static file: xfwb.html [in /var/www/xmlmaker/backend/app.py:697]
2025-06-12 10:43:10,399 INFO: XFWB sample data requested [in /var/www/xmlmaker/backend/app.py:563]
2025-06-12 10:43:10,399 INFO: XFWB sample data sent [in /var/www/xmlmaker/backend/app.py:569]
2025-06-12 10:50:55,004 INFO: Serving static file: xfwb.html [in /var/www/xmlmaker/backend/app.py:697]
2025-06-12 10:51:02,756 INFO: Received XFWB XML generation request [in /var/www/xmlmaker/backend/app.py:436]
2025-06-12 10:51:02,756 INFO: Input text length: 1184 [in /var/www/xmlmaker/backend/app.py:443]
2025-06-12 10:51:02,757 INFO: Generating XFWB XML... [in /var/www/xmlmaker/backend/app.py:453]
2025-06-12 10:51:02,773 INFO: XFWB XML generated successfully, length: 96037 [in /var/www/xmlmaker/backend/app.py:465]
2025-06-12 10:51:02,773 INFO: Multiple AWBs detected in the response [in /var/www/xmlmaker/backend/app.py:475]
2025-06-12 10:51:02,773 INFO: Found 20 AWBs in the response [in /var/www/xmlmaker/backend/app.py:479]
2025-06-12 11:55:33,528 INFO: Serving static file: xfwb.html [in /var/www/xmlmaker/backend/app.py:697]
2025-06-12 12:43:29,267 INFO: Serving static file: xfzb.html [in /var/www/xmlmaker/backend/app.py:697]
2025-06-13 11:16:17,400 INFO: IATA XML Generator startup [in /var/www/xmlmaker/backend/app.py:36]
2025-06-13 11:16:17,404 INFO: IATA XML Generator startup [in /var/www/xmlmaker/backend/app.py:36]
2025-06-13 11:16:17,407 INFO: Static folder path: /var/www/xmlmaker/frontend/static [in /var/www/xmlmaker/backend/app.py:37]
2025-06-13 11:16:17,413 INFO: Static folder path: /var/www/xmlmaker/frontend/static [in /var/www/xmlmaker/backend/app.py:37]
2025-06-13 11:16:17,413 INFO: IATA XML Generator startup [in /var/www/xmlmaker/backend/app.py:36]
2025-06-13 11:16:17,413 INFO: Static folder path: /var/www/xmlmaker/frontend/static [in /var/www/xmlmaker/backend/app.py:37]
2025-06-13 11:16:17,433 INFO: IATA XML Generator startup [in /var/www/xmlmaker/backend/app.py:36]
2025-06-13 11:16:17,433 INFO: Static folder path: /var/www/xmlmaker/frontend/static [in /var/www/xmlmaker/backend/app.py:37]
2025-06-13 11:21:27,155 INFO: Serving static file: favicon.ico [in /var/www/xmlmaker/backend/app.py:697]
2025-06-13 12:27:29,987 INFO: Serving static file: xfwb.html [in /var/www/xmlmaker/backend/app.py:697]
2025-06-13 16:07:37,460 INFO: Serving index.html [in /var/www/xmlmaker/backend/app.py:691]
2025-06-13 16:07:40,723 INFO: Serving static file: xfzb.html [in /var/www/xmlmaker/backend/app.py:697]
