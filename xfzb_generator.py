#!/usr/bin/env python3
"""
XFZB (House Waybill) XML generator.
This module generates XFZB XML documents from simplified input format.
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
import re
import uuid
import datetime
import logging
import os

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, 'xfzb_generator.log')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('xfzb_generator')

class XFZBGenerator:
    def __init__(self):
        # Define XML namespaces
        self.rsm = "iata:housewaybill:1"
        self.ram = "iata:datamodel:3"

        # Register namespaces for proper output formatting
        ET.register_namespace('rsm', self.rsm)
        ET.register_namespace('ram', self.ram)

        # Clear any existing namespace prefixes
        for prefix in list(ET._namespace_map.keys()):
            if prefix in [self.rsm, self.ram]:
                del ET._namespace_map[prefix]

    def create_house_waybill_xml(self, input_text, config=None):
        """Parse input text and create XFZB XML document"""
        try:
            # Parse the house waybill information from input text
            house_waybill_info = self._parse_house_waybill_info(input_text)

            # Check if we have any House AWBs
            if not house_waybill_info.get('house_awbs'):
                logger.warning("No House AWBs found to generate XML")
                return "<!-- No valid House AWBs found to generate XML -->"

            # If we have multiple House AWBs, generate a consolidated XML document
            if len(house_waybill_info['house_awbs']) > 1:
                return self._create_consolidated_xml(house_waybill_info, config)

            # Apply any configuration overrides for single House AWB
            if config:
                house_waybill_info.update(config)

            # Create root element with namespaces
            root = ET.Element(f"{{{self.rsm}}}HouseWaybill")
            # Add namespace attributes
            root.set("xmlns:rsm", self.rsm)
            root.set("xmlns:ram", self.ram)

            # Add header documents
            self._add_message_header(root, house_waybill_info)
            self._add_business_header(root, house_waybill_info)

            # Add master consignment
            self._add_master_consignment(root, house_waybill_info)

            # Return formatted XML
            return self._format_xml(root)
        except Exception as e:
            logger.error(f"Error creating XFZB XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating XFZB XML: {str(e)} -->"

    def _create_consolidated_xml(self, house_waybill_info, config=None):
        """Create a consolidated XML document for multiple House AWBs"""
        try:
            logger.info(f"Generating consolidated XML for {len(house_waybill_info['house_awbs'])} House AWBs")

            # Create a container for all House AWB XMLs
            consolidated_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
            consolidated_xml += '<HouseWaybillCollection>\n'

            # Process each House AWB
            for i, house_awb in enumerate(house_waybill_info['house_awbs']):
                logger.info(f"Processing House AWB {i+1}/{len(house_waybill_info['house_awbs'])}: {house_awb['house_awb_number']}")

                # Create a copy of the house waybill info for this House AWB
                single_house_awb_info = house_waybill_info.copy()

                # Update with this specific House AWB's data
                for key, value in house_awb.items():
                    single_house_awb_info[key] = value

                # Apply any configuration overrides
                if config:
                    # Only apply non-House AWB specific config
                    for key, value in config.items():
                        if key not in house_awb:
                            single_house_awb_info[key] = value

                # Create root element with namespaces
                root = ET.Element(f"{{{self.rsm}}}HouseWaybill")
                # Add namespace attributes
                root.set("xmlns:rsm", self.rsm)
                root.set("xmlns:ram", self.ram)

                # Add header documents
                self._add_message_header(root, single_house_awb_info)
                self._add_business_header(root, single_house_awb_info)

                # Add master consignment
                self._add_master_consignment(root, single_house_awb_info)

                # Format the XML for this House AWB
                house_awb_xml = self._format_xml(root)

                # Remove XML declaration from individual House AWBs (except the first one)
                if i > 0:
                    house_awb_xml = house_awb_xml.replace('<?xml version="1.0" encoding="UTF-8"?>\n', '')

                # Add to consolidated XML
                consolidated_xml += house_awb_xml + '\n'

            consolidated_xml += '</HouseWaybillCollection>'
            return consolidated_xml

        except Exception as e:
            logger.error(f"Error creating consolidated XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating consolidated XML: {str(e)} -->"

    def _parse_house_waybill_info(self, input_text):
        """Parse the input text to extract house waybill information"""
        logger.info(f"Parsing input text: {input_text}")

        # Initialize dictionary to store house waybill information with default values
        house_waybill_info = {
            'house_awbs': [],  # List to store multiple House AWBs
            'issue_date': datetime.datetime.now().isoformat(),
            'master_awb_number': '724-42499866',
            'consignor_name': 'IEI INTEGRATION CORP',
            'consignor_address': '29 JHONGSING RD SIJHIH CITY',
            'consignor_city': 'TAIPEI',
            'consignor_country': 'TAIWAN',
            'consignor_country_code': 'TW',
            'consignor_postcode': '221',
            'consignee_name': 'CAPTEC LIMITED',
            'consignee_address': '7 WHITTLE AVENUE SEGENSWORTH',
            'consignee_city': 'FAREHAM',
            'consignee_country': 'UNITED KINGDOM',
            'consignee_country_code': 'GB',
            'consignee_postcode': 'PO15 5SH',
            'freight_forwarder_name': 'KUEHNE + NAGEL LIMITED',
            'freight_forwarder_id': '0837059/0211',
            'freight_forwarder_address': '5,6F, OFFICE BLDG B, NO. 1909 JIANG ZHEN RD,SHANGHAI AIRL.TRAVEL HOTEL',
            'freight_forwarder_city': 'SHANGHAI',
            'freight_forwarder_country': 'CHINA',
            'freight_forwarder_country_code': 'CN'
        }

        # Parse the input text
        # Format examples:
        # "SHAS51282599/T1K4.0MC0.027/panel pc/EAW"
        # "*********/T8K180.0MC0.0/MEDICAL SUPPLIES/DGR"

        try:
            # Split by lines in case multiple entries are provided
            lines = input_text.strip().split('\n')

            # Pattern for House AWB line: {house_awb_number}/T{pieces}K{weight}MC{volume}/{description}
            house_awb_pattern = r'([A-Z0-9]+)\/([ST])(\d+)K(\d+\.\d+)MC(\d+\.\d+)\/(.*)'

            # Process all lines to find House AWBs and their Handling Instructions codes
            current_house_awb = None

            for i in range(len(lines)):
                line = lines[i].strip()

                # Skip empty lines
                if not line:
                    continue

                # Check if this is a House AWB line
                match = re.match(house_awb_pattern, line)
                if match:
                    # Create a new House AWB entry
                    house_awb_number = match.group(1)
                    split_code = match.group(2)  # S or T
                    pieces = int(match.group(3))
                    weight = float(match.group(4))
                    volume = float(match.group(5))
                    description = match.group(6).strip()

                    current_house_awb = {
                        'house_awb_number': house_awb_number,
                        'pieces': pieces,
                        'weight': weight,
                        'volume': volume,
                        'description': description,
                        'split_code': split_code,
                        'special_handling_codes': []
                    }

                    house_waybill_info['house_awbs'].append(current_house_awb)
                    logger.info(f"Found House AWB: {current_house_awb['house_awb_number']}")

                # Check if this is a Handling Instructions code line for the current House AWB
                elif line.startswith('/') and current_house_awb is not None:
                    # This is a Handling Instructions code line
                    codes = line.strip('/').split('/')
                    current_house_awb['special_handling_codes'].extend([code.strip() for code in codes if code.strip()])
                    logger.info(f"Added Handling Instructions codes to House AWB {current_house_awb['house_awb_number']}: {codes}")

            # If no House AWBs were found, log a warning
            if not house_waybill_info['house_awbs']:
                logger.warning("No valid House AWBs found in the input text")
                return house_waybill_info

            # Set the primary House AWB (first one) as the main house waybill info for backward compatibility
            primary_house_awb = house_waybill_info['house_awbs'][0]
            for key, value in primary_house_awb.items():
                house_waybill_info[key] = value

            logger.info(f"Parsed {len(house_waybill_info['house_awbs'])} House AWBs")
            return house_waybill_info

        except Exception as e:
            logger.error(f"Error parsing house waybill info: {str(e)}", exc_info=True)
            raise

    def _add_message_header(self, root, house_waybill_info):
        """Add MessageHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}MessageHeaderDocument")

        # Add House AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = house_waybill_info.get('house_awb_number', '')

        # Add document name
        ET.SubElement(header, f"{{{self.ram}}}Name").text = "House waybill"

        # Add type code (703 for House Waybill)
        ET.SubElement(header, f"{{{self.ram}}}TypeCode").text = "703"

        # Add issue date/time
        ET.SubElement(header, f"{{{self.ram}}}IssueDateTime").text = house_waybill_info.get('issue_date', datetime.datetime.now().isoformat())

        # Add purpose code
        ET.SubElement(header, f"{{{self.ram}}}PurposeCode").text = "Creation"

        # Add version ID
        ET.SubElement(header, f"{{{self.ram}}}VersionID").text = "3.00"

        # Add conversation ID
        ET.SubElement(header, f"{{{self.ram}}}ConversationID").text = str(uuid.uuid4())[:12]

        # Add sender party
        sender = ET.SubElement(header, f"{{{self.ram}}}SenderParty")
        ET.SubElement(sender, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "TDVAGT03KUEHNENAGEL/SHA1"

        # Add recipient party
        recipient = ET.SubElement(header, f"{{{self.ram}}}RecipientParty")
        ET.SubElement(recipient, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "TDVCCS98CCSTDV"

    def _add_business_header(self, root, house_waybill_info):
        """Add BusinessHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}BusinessHeaderDocument")

        # Add House AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = house_waybill_info.get('house_awb_number', '')

        # Add signatory consignor authentication
        consignor_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryConsignorAuthentication")
        ET.SubElement(consignor_auth, f"{{{self.ram}}}Signatory").text = house_waybill_info.get('freight_forwarder_name', 'KUEHNE + NAGEL LIMITED')

        # Add signatory carrier authentication
        carrier_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryCarrierAuthentication")
        ET.SubElement(carrier_auth, f"{{{self.ram}}}ActualDateTime").text = house_waybill_info.get('issue_date', datetime.datetime.now().isoformat())
        ET.SubElement(carrier_auth, f"{{{self.ram}}}Signatory").text = house_waybill_info.get('freight_forwarder_name', 'KUEHNE + NAGEL LI')

        # Add issue authentication location
        loc = ET.SubElement(carrier_auth, f"{{{self.ram}}}IssueAuthenticationLocation")
        ET.SubElement(loc, f"{{{self.ram}}}Name").text = "PVG"

    def _add_master_consignment(self, root, house_waybill_info):
        """Add MasterConsignment to the XML"""
        consignment = ET.SubElement(root, f"{{{self.rsm}}}MasterConsignment")

        # Add weight and total pieces for master consignment
        ET.SubElement(consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure", unitCode="KGM").text = str(house_waybill_info.get('weight', 4.0))
        ET.SubElement(consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(house_waybill_info.get('pieces', 1))

        # Add transport contract document (Master AWB)
        transport_doc = ET.SubElement(consignment, f"{{{self.ram}}}TransportContractDocument")
        ET.SubElement(transport_doc, f"{{{self.ram}}}ID").text = house_waybill_info.get('master_awb_number', '724-42499866')

        # Add origin location
        origin = ET.SubElement(consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = "PVG"

        # Add destination location
        destination = ET.SubElement(consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = "LHR"

        # Add included house consignment
        self._add_house_consignment(consignment, house_waybill_info)

    def _add_house_consignment(self, consignment, house_waybill_info):
        """Add IncludedHouseConsignment to the XML"""
        house_consignment = ET.SubElement(consignment, f"{{{self.ram}}}IncludedHouseConsignment")

        # Add House AWB ID
        ET.SubElement(house_consignment, f"{{{self.ram}}}ID").text = house_waybill_info.get('house_awb_number', '')

        # Add weight, volume, and pieces
        ET.SubElement(house_consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure", unitCode="KGM").text = str(house_waybill_info.get('weight', 4.0))
        ET.SubElement(house_consignment, f"{{{self.ram}}}GrossVolumeMeasure", unitCode="MTQ").text = str(house_waybill_info.get('volume', 0.027))
        ET.SubElement(house_consignment, f"{{{self.ram}}}PackageQuantity").text = str(house_waybill_info.get('pieces', 1))
        ET.SubElement(house_consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(house_waybill_info.get('pieces', 1))

        # Add summary description
        ET.SubElement(house_consignment, f"{{{self.ram}}}SummaryDescription").text = house_waybill_info.get('description', 'panel pc')

        # Add parties
        self._add_consignor_party(house_consignment, house_waybill_info)
        self._add_consignee_party(house_consignment, house_waybill_info)
        self._add_freight_forwarder_party(house_consignment, house_waybill_info)

        # Add origin and destination locations
        origin = ET.SubElement(house_consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = "PVG"

        destination = ET.SubElement(house_consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = "LHR"

        # Add transport movements
        self._add_transport_movements(house_consignment, house_waybill_info)

        # Add handling instructions
        self._add_handling_instructions(house_consignment, house_waybill_info)

        # Add currency exchange
        currency = ET.SubElement(house_consignment, f"{{{self.ram}}}ApplicableOriginCurrencyExchange")
        ET.SubElement(currency, f"{{{self.ram}}}SourceCurrencyCode").text = "CNY"

    def _add_consignor_party(self, house_consignment, house_waybill_info):
        """Add ConsignorParty to the XML"""
        consignor = ET.SubElement(house_consignment, f"{{{self.ram}}}ConsignorParty")
        ET.SubElement(consignor, f"{{{self.ram}}}Name").text = house_waybill_info.get('consignor_name', 'IEI INTEGRATION CORP')

        address = ET.SubElement(consignor, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = house_waybill_info.get('consignor_postcode', '221')
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = house_waybill_info.get('consignor_address', '29 JHONGSING RD SIJHIH CITY')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = house_waybill_info.get('consignor_city', 'TAIPEI')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = house_waybill_info.get('consignor_country_code', 'TW')

        # Add defined trade contact
        ET.SubElement(consignor, f"{{{self.ram}}}DefinedTradeContact")

    def _add_consignee_party(self, house_consignment, house_waybill_info):
        """Add ConsigneeParty to the XML"""
        consignee = ET.SubElement(house_consignment, f"{{{self.ram}}}ConsigneeParty")
        ET.SubElement(consignee, f"{{{self.ram}}}Name").text = house_waybill_info.get('consignee_name', 'CAPTEC LIMITED')

        address = ET.SubElement(consignee, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = house_waybill_info.get('consignee_postcode', 'PO15 5SH')
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = house_waybill_info.get('consignee_address', '7 WHITTLE AVENUE SEGENSWORTH')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = house_waybill_info.get('consignee_city', 'FAREHAM')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = house_waybill_info.get('consignee_country_code', 'GB')

        # Add defined trade contact
        ET.SubElement(consignee, f"{{{self.ram}}}DefinedTradeContact")

    def _add_freight_forwarder_party(self, house_consignment, house_waybill_info):
        """Add FreightForwarderParty to the XML"""
        forwarder = ET.SubElement(house_consignment, f"{{{self.ram}}}FreightForwarderParty")
        ET.SubElement(forwarder, f"{{{self.ram}}}Name").text = house_waybill_info.get('freight_forwarder_name', 'KUEHNE + NAGEL LIMITED')
        ET.SubElement(forwarder, f"{{{self.ram}}}AccountID").text = house_waybill_info.get('freight_forwarder_id', '0837059/0211')

        address = ET.SubElement(forwarder, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = house_waybill_info.get('freight_forwarder_address', '5,6F, OFFICE BLDG B, NO. 1909 JIANG ZHEN RD,SHANGHAI AIRL.TRAVEL HOTEL')
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = house_waybill_info.get('freight_forwarder_city', 'SHANGHAI')
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = house_waybill_info.get('freight_forwarder_country_code', 'CN')

        # Add defined trade contact
        ET.SubElement(forwarder, f"{{{self.ram}}}DefinedTradeContact")

    def _add_transport_movements(self, house_consignment, house_waybill_info):
        """Add SpecifiedLogisticsTransportMovement to the XML"""
        # First transport movement
        movement1 = ET.SubElement(house_consignment, f"{{{self.ram}}}SpecifiedLogisticsTransportMovement")
        ET.SubElement(movement1, f"{{{self.ram}}}StageCode").text = "MAIN-CARRIAGE"
        ET.SubElement(movement1, f"{{{self.ram}}}ID").text = "LX189"
        ET.SubElement(movement1, f"{{{self.ram}}}SequenceNumeric").text = "1"

        # Used transport means
        transport_means1 = ET.SubElement(movement1, f"{{{self.ram}}}UsedLogisticsTransportMeans")
        ET.SubElement(transport_means1, f"{{{self.ram}}}Name").text = "LX"

        # Arrival event
        arrival1 = ET.SubElement(movement1, f"{{{self.ram}}}ArrivalEvent")
        arrival_location1 = ET.SubElement(arrival1, f"{{{self.ram}}}OccurrenceArrivalLocation")
        ET.SubElement(arrival_location1, f"{{{self.ram}}}ID").text = "ZRH"
        ET.SubElement(arrival_location1, f"{{{self.ram}}}TypeCode").text = "AIRPORT"

        # Departure event
        departure1 = ET.SubElement(movement1, f"{{{self.ram}}}DepartureEvent")
        ET.SubElement(departure1, f"{{{self.ram}}}ScheduledOccurrenceDateTime").text = "2014-07-05T10:17:00"

        # Second transport movement
        movement2 = ET.SubElement(house_consignment, f"{{{self.ram}}}SpecifiedLogisticsTransportMovement")
        ET.SubElement(movement2, f"{{{self.ram}}}StageCode").text = "MAIN-CARRIAGE"
        ET.SubElement(movement2, f"{{{self.ram}}}ID").text = "LX338"
        ET.SubElement(movement2, f"{{{self.ram}}}SequenceNumeric").text = "2"

        # Used transport means
        transport_means2 = ET.SubElement(movement2, f"{{{self.ram}}}UsedLogisticsTransportMeans")
        ET.SubElement(transport_means2, f"{{{self.ram}}}Name").text = "LX"

        # Arrival event
        arrival2 = ET.SubElement(movement2, f"{{{self.ram}}}ArrivalEvent")
        arrival_location2 = ET.SubElement(arrival2, f"{{{self.ram}}}OccurrenceArrivalLocation")
        ET.SubElement(arrival_location2, f"{{{self.ram}}}ID").text = "LHR"
        ET.SubElement(arrival_location2, f"{{{self.ram}}}TypeCode").text = "AIRPORT"

        # Departure event
        departure2 = ET.SubElement(movement2, f"{{{self.ram}}}DepartureEvent")
        ET.SubElement(departure2, f"{{{self.ram}}}ScheduledOccurrenceDateTime").text = "2014-07-07T15:40:00"

    def _add_handling_instructions(self, house_consignment, house_waybill_info):
        """Add handling instructions to the XML"""
        # Add default handling instruction
        handling = ET.SubElement(house_consignment, f"{{{self.ram}}}HandlingSPHInstructions")
        ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = "EAW"

        # Add special handling codes if any
        for code in house_waybill_info.get('special_handling_codes', []):
            handling = ET.SubElement(house_consignment, f"{{{self.ram}}}HandlingSPHInstructions")
            ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = code

        # Add SSR instructions
        ssr = ET.SubElement(house_consignment, f"{{{self.ram}}}HandlingSSRInstructions")
        ET.SubElement(ssr, f"{{{self.ram}}}Description").text = "FREIGHT COLLECT COPY INV AND P/L ATTD"

        # Add accounting note
        note = ET.SubElement(house_consignment, f"{{{self.ram}}}IncludedAccountingNote")
        ET.SubElement(note, f"{{{self.ram}}}ContentCode").text = "GEN"
        ET.SubElement(note, f"{{{self.ram}}}Content").text = "NOTIFY :KUEHNE + NAGEL LTD. 4/F, 219, NANKING EAST ROAD SECTION 3, TAIPEI TAIWAN R.O.C. TAIPEI - TAIWAN R.O.C."

    def _format_xml(self, root):
        """Convert ElementTree to pretty-printed XML string"""
        try:
            # Use ElementTree's built-in pretty printing
            ET.indent(ET.ElementTree(root), space="  ")

            # Convert to string and manually fix the namespace issue
            rough_string = ET.tostring(root, 'utf-8').decode('utf-8')

            # Remove duplicate namespace declarations
            rough_string = rough_string.replace(' xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3" xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3"',
                                              ' xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3"')

            # Add XML declaration
            xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'
            return xml_declaration + rough_string
        except Exception as e:
            logger.error(f"Error formatting XML: {str(e)}", exc_info=True)
            return f"<!-- Error formatting XML: {str(e)} -->"


def generate_xfzb_from_text(input_text, config=None):
    """Generate XFZB XML from the input text format"""
    try:
        # Add default values for required fields
        if not input_text or len(input_text.strip()) < 5:
            input_text = "SHAS51282599/T1K4.0MC0.027/panel pc\n/EAW"

        generator = XFZBGenerator()
        result = generator.create_house_waybill_xml(input_text, config)
        return result
    except Exception as e:
        import traceback
        error_msg = f"Error generating XFZB XML: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"<!-- {error_msg} -->"


if __name__ == "__main__":
    # Example input
    sample_input = "SHAS51282599/T1K4.0MC0.027/panel pc\n/EAW"

    # Generate XML
    xml_output = generate_xfzb_from_text(sample_input)

    # Print or save to file
    print(xml_output)

    # Optionally save to file
    with open("generated_house_waybill.xml", "w", encoding="utf-8") as f:
        f.write(xml_output)
