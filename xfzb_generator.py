#!/usr/bin/env python3
"""
XFZB (House Waybill) XML generator.
This module generates XFZB XML documents from pipe-delimited input format.

Input format: {HAWB#} | {MAWB#} | {CARRIER}+{FLIGHT#} | {DATETIME} | {PIECES} | {PACKAGES} | {WEIGHT} | {UnitMeasure} | {SOURCE}-{DESTINATION} | {DESCRIPTION} | {SHC}
Example: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
"""

import xml.etree.ElementTree as ET
import datetime
import logging
import os
import random
import string

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, 'xfzb_generator.log')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('xfzb_generator')

class XFZBGenerator:
    def __init__(self):
        # Define XML namespaces
        self.rsm = "iata:housewaybill:1"
        self.ram = "iata:datamodel:3"

        # Register namespaces for proper output formatting
        ET.register_namespace('rsm', self.rsm)
        ET.register_namespace('ram', self.ram)

        # Clear any existing namespace prefixes
        for prefix in list(ET._namespace_map.keys()):
            if prefix in [self.rsm, self.ram]:
                del ET._namespace_map[prefix]

    def generate_conversation_id(self):
        """Generate a random 24-character conversation ID"""
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=24))

    def create_house_waybill_xml(self, input_text, config=None):
        """Parse input text and create XFZB XML document"""
        try:
            # Parse the house waybill information from input text
            house_waybill_info = self._parse_house_waybill_info(input_text)

            # Apply any configuration overrides
            if config:
                house_waybill_info.update(config)

            # Create root element with namespaces
            root = ET.Element(f"{{{self.rsm}}}HouseWaybill")
            # Add namespace attributes
            root.set("xmlns:rsm", self.rsm)
            root.set("xmlns:ram", self.ram)

            # Add header documents
            self._add_message_header(root, house_waybill_info)
            self._add_business_header(root, house_waybill_info)

            # Add master consignment
            self._add_master_consignment(root, house_waybill_info)

            # Return formatted XML
            return self._format_xml(root)
        except Exception as e:
            logger.error(f"Error creating XFZB XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating XFZB XML: {str(e)} -->"

    def _parse_house_waybill_info(self, input_text):
        """Parse the pipe-delimited input text to extract house waybill information

        Format: {HAWB#} | {MAWB#} | {CARRIER}+{FLIGHT#} | {DATETIME} | {PIECES} | {PACKAGES} | {WEIGHT} | {UnitMeasure} | {SOURCE}-{DESTINATION} | {DESCRIPTION} | {SHC}
        Example: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
        """
        logger.info(f"Parsing input text: {input_text}")

        try:
            # Split the input by pipe delimiter
            parts = [part.strip() for part in input_text.split('|')]

            if len(parts) != 11:
                raise ValueError(f"Expected 11 pipe-delimited fields, got {len(parts)}")

            # Extract fields
            hawb = parts[0]
            mawb = parts[1]
            carrier_flight = parts[2]
            datetime_str = parts[3]
            pieces = int(parts[4])
            packages = int(parts[5])
            weight = float(parts[6])
            unit_measure = float(parts[7])
            source_dest = parts[8]
            description = parts[9]
            shc = parts[10]

            # Parse carrier and flight
            if '+' in carrier_flight:
                carrier, flight = carrier_flight.split('+', 1)
            else:
                # Fallback: assume first 2 chars are carrier
                carrier = carrier_flight[:2]
                flight = carrier_flight[2:]

            # Parse source and destination
            if '-' in source_dest:
                source, destination = source_dest.split('-', 1)
            else:
                raise ValueError(f"Invalid source-destination format: {source_dest}")

            # Parse datetime and create issue/actual datetime
            dt = datetime.datetime.fromisoformat(datetime_str.replace('T', ' '))
            issue_dt = dt - datetime.timedelta(hours=1)  # 1 hour before

            # Parse SHC codes (can be multiple separated by /)
            shc_codes = [code.strip() for code in shc.split('/') if code.strip()]

            # Generate conversation ID
            conversation_id = self.generate_conversation_id()

            # Create house waybill info dictionary
            house_waybill_info = {
                'hawb': hawb,
                'mawb': mawb,
                'carrier': carrier,
                'flight': flight,
                'carrier_flight': carrier_flight,
                'datetime': datetime_str,
                'issue_datetime': issue_dt.strftime('%Y-%m-%dT%H:%M:%S'),
                'actual_datetime': dt.strftime('%Y-%m-%dT%H:%M:%S'),
                'pieces': pieces,
                'packages': packages,
                'weight': weight,
                'unit_measure': unit_measure,
                'source': source,
                'destination': destination,
                'description': description,
                'shc_codes': shc_codes,
                'conversation_id': conversation_id
            }

            logger.info(f"Parsed HAWB: {hawb}, MAWB: {mawb}, Route: {source}-{destination}")
            return house_waybill_info

        except Exception as e:
            logger.error(f"Error parsing house waybill info: {str(e)}", exc_info=True)
            raise

    def _add_message_header(self, root, house_waybill_info):
        """Add MessageHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}MessageHeaderDocument")

        # Add House AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = house_waybill_info.get('hawb', '')

        # Add document name
        ET.SubElement(header, f"{{{self.ram}}}Name").text = "HouseWaybill"

        # Add type code (703 for House Waybill)
        ET.SubElement(header, f"{{{self.ram}}}TypeCode").text = "703"

        # Add issue date/time (1 hour before actual datetime)
        ET.SubElement(header, f"{{{self.ram}}}IssueDateTime").text = house_waybill_info.get('issue_datetime', '')

        # Add purpose code
        ET.SubElement(header, f"{{{self.ram}}}PurposeCode").text = "Creation"

        # Add version ID
        ET.SubElement(header, f"{{{self.ram}}}VersionID").text = "3.00"

        # Add conversation ID (24 character random string)
        ET.SubElement(header, f"{{{self.ram}}}ConversationID").text = house_waybill_info.get('conversation_id', '')

        # Add sender party
        sender = ET.SubElement(header, f"{{{self.ram}}}SenderParty")
        hawb = house_waybill_info.get('hawb', '')
        ET.SubElement(sender, f"{{{self.ram}}}PrimaryID", schemeID="C").text = f"TDVAGT03KUEHNENAGEL/{hawb}"

        # Add recipient party
        recipient = ET.SubElement(header, f"{{{self.ram}}}RecipientParty")
        ET.SubElement(recipient, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "TDVCCS98CCSTDV"

    def _add_business_header(self, root, house_waybill_info):
        """Add BusinessHeaderDocument to the XML"""
        header = ET.SubElement(root, f"{{{self.rsm}}}BusinessHeaderDocument")

        # Add House AWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = house_waybill_info.get('hawb', '')

        # Add signatory consignor authentication
        consignor_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryConsignorAuthentication")
        ET.SubElement(consignor_auth, f"{{{self.ram}}}Signatory").text = "KUEHNE + NAGEL LIMITED"

        # Add signatory carrier authentication
        carrier_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryCarrierAuthentication")
        ET.SubElement(carrier_auth, f"{{{self.ram}}}ActualDateTime").text = house_waybill_info.get('actual_datetime', '')
        ET.SubElement(carrier_auth, f"{{{self.ram}}}Signatory").text = "KUEHNE + NAGEL LI"

        # Add issue authentication location
        loc = ET.SubElement(carrier_auth, f"{{{self.ram}}}IssueAuthenticationLocation")
        ET.SubElement(loc, f"{{{self.ram}}}Name").text = house_waybill_info.get('source', '')

    def _add_master_consignment(self, root, house_waybill_info):
        """Add MasterConsignment to the XML"""
        consignment = ET.SubElement(root, f"{{{self.rsm}}}MasterConsignment")

        # Add weight and total pieces for master consignment
        weight = house_waybill_info.get('weight', 10.0)
        pieces = house_waybill_info.get('pieces', 1)
        ET.SubElement(consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure", unitCode="KGM").text = str(weight)
        ET.SubElement(consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(pieces)

        # Add transport contract document (Master AWB)
        transport_doc = ET.SubElement(consignment, f"{{{self.ram}}}TransportContractDocument")
        ET.SubElement(transport_doc, f"{{{self.ram}}}ID").text = house_waybill_info.get('mawb', '')

        # Add origin location
        origin = ET.SubElement(consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = house_waybill_info.get('source', '')

        # Add destination location
        destination = ET.SubElement(consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = house_waybill_info.get('destination', '')

        # Add included house consignment
        self._add_house_consignment(consignment, house_waybill_info)

    def _add_house_consignment(self, consignment, house_waybill_info):
        """Add IncludedHouseConsignment to the XML"""
        house_consignment = ET.SubElement(consignment, f"{{{self.ram}}}IncludedHouseConsignment")

        # Add House AWB ID
        ET.SubElement(house_consignment, f"{{{self.ram}}}ID").text = house_waybill_info.get('hawb', '')

        # Add weight, volume, and pieces
        weight = house_waybill_info.get('weight', 10.0)
        unit_measure = house_waybill_info.get('unit_measure', 1.01)
        packages = house_waybill_info.get('packages', 10)
        pieces = house_waybill_info.get('pieces', 1)

        ET.SubElement(house_consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure", unitCode="KGM").text = str(weight)
        ET.SubElement(house_consignment, f"{{{self.ram}}}GrossVolumeMeasure", unitCode="MTQ").text = str(unit_measure)
        ET.SubElement(house_consignment, f"{{{self.ram}}}PackageQuantity").text = str(packages)
        ET.SubElement(house_consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(pieces)

        # Add summary description
        ET.SubElement(house_consignment, f"{{{self.ram}}}SummaryDescription").text = house_waybill_info.get('description', '')

        # Add parties
        self._add_consignor_party(house_consignment, house_waybill_info)
        self._add_consignee_party(house_consignment, house_waybill_info)
        self._add_freight_forwarder_party(house_consignment, house_waybill_info)
        self._add_associated_party(house_consignment, house_waybill_info)

        # Add origin and destination locations
        origin = ET.SubElement(house_consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = house_waybill_info.get('source', '')

        destination = ET.SubElement(house_consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = house_waybill_info.get('destination', '')

        # Add transport movements
        self._add_transport_movements(house_consignment, house_waybill_info)

        # Add handling instructions
        self._add_handling_instructions(house_consignment, house_waybill_info)

        # Add accounting note
        self._add_accounting_note(house_consignment, house_waybill_info)

        # Add currency exchange
        currency = ET.SubElement(house_consignment, f"{{{self.ram}}}ApplicableOriginCurrencyExchange")
        ET.SubElement(currency, f"{{{self.ram}}}SourceCurrencyCode").text = "USD"

    def _add_consignor_party(self, house_consignment, house_waybill_info):
        """Add ConsignorParty to the XML"""
        consignor = ET.SubElement(house_consignment, f"{{{self.ram}}}ConsignorParty")
        ET.SubElement(consignor, f"{{{self.ram}}}Name").text = "IEI INTEGRATION CORP"

        address = ET.SubElement(consignor, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = "221"
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = "29 JHONGSING RD SIJHIH CITY "
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "TAIPEI"
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = "TW"

        # Add defined trade contact
        ET.SubElement(consignor, f"{{{self.ram}}}DefinedTradeContact")

    def _add_consignee_party(self, house_consignment, house_waybill_info):
        """Add ConsigneeParty to the XML"""
        consignee = ET.SubElement(house_consignment, f"{{{self.ram}}}ConsigneeParty")
        ET.SubElement(consignee, f"{{{self.ram}}}Name").text = "CAPTEC LIMITED"

        address = ET.SubElement(consignee, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = "PO15 5SH"
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = "7 WHITTLE AVENUE SEGENSWORTH"
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "FAREHAM"
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = "GB"

        # Add defined trade contact
        ET.SubElement(consignee, f"{{{self.ram}}}DefinedTradeContact")

    def _add_freight_forwarder_party(self, house_consignment, house_waybill_info):
        """Add FreightForwarderParty to the XML"""
        forwarder = ET.SubElement(house_consignment, f"{{{self.ram}}}FreightForwarderParty")
        ET.SubElement(forwarder, f"{{{self.ram}}}Name").text = "KUEHNE + NAGEL LIMITED"
        ET.SubElement(forwarder, f"{{{self.ram}}}AccountID").text = "0837059/0211"

        address = ET.SubElement(forwarder, f"{{{self.ram}}}PostalStructuredAddress")
        street_text = "5,6F, OFFICE BLDG B, NO. 1909 JIANG ZHEN RD,SHANGHAI AIRL.TRAVEL HOTEL"
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = street_text
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "SHANGHAI"
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = "CN"

        # Add defined trade contact
        ET.SubElement(forwarder, f"{{{self.ram}}}DefinedTradeContact")

    def _add_associated_party(self, house_consignment, house_waybill_info):
        """Add AssociatedParty to the XML"""
        party = ET.SubElement(house_consignment, f"{{{self.ram}}}AssociatedParty")
        ET.SubElement(party, f"{{{self.ram}}}Name").text = "KUEHNE + NAGEL LTD."
        ET.SubElement(party, f"{{{self.ram}}}RoleCode").text = "NI"

        address = ET.SubElement(party, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = "4/F, 219, NANKING EAST ROAD SECTION 3, TAIPEI"
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "TAIPEI"
        ET.SubElement(address, f"{{{self.ram}}}CountryID").text = "TW"

        # Add defined trade contact
        ET.SubElement(party, f"{{{self.ram}}}DefinedTradeContact")

    def _add_transport_movements(self, house_consignment, house_waybill_info):
        """Add SpecifiedLogisticsTransportMovement to the XML"""
        # Single transport movement based on input data
        movement = ET.SubElement(house_consignment, f"{{{self.ram}}}SpecifiedLogisticsTransportMovement")
        ET.SubElement(movement, f"{{{self.ram}}}StageCode").text = "MAIN-CARRIAGE"

        # Use carrier+flight from input
        carrier_flight = house_waybill_info.get('carrier_flight', 'KQ7001')
        ET.SubElement(movement, f"{{{self.ram}}}ID").text = carrier_flight
        ET.SubElement(movement, f"{{{self.ram}}}SequenceNumeric").text = "1"

        # Used transport means
        carrier = house_waybill_info.get('carrier', 'KQ')
        transport_means = ET.SubElement(movement, f"{{{self.ram}}}UsedLogisticsTransportMeans")
        ET.SubElement(transport_means, f"{{{self.ram}}}Name").text = carrier

        # Arrival event
        arrival = ET.SubElement(movement, f"{{{self.ram}}}ArrivalEvent")
        arrival_location = ET.SubElement(arrival, f"{{{self.ram}}}OccurrenceArrivalLocation")
        destination = house_waybill_info.get('destination', 'LLW')
        ET.SubElement(arrival_location, f"{{{self.ram}}}ID").text = destination
        ET.SubElement(arrival_location, f"{{{self.ram}}}TypeCode").text = "AIRPORT"

        # Departure event
        departure = ET.SubElement(movement, f"{{{self.ram}}}DepartureEvent")
        datetime_str = house_waybill_info.get('datetime', '2014-07-07T11:38:25')
        ET.SubElement(departure, f"{{{self.ram}}}ScheduledOccurrenceDateTime").text = datetime_str

    def _add_handling_instructions(self, house_consignment, house_waybill_info):
        """Add handling instructions to the XML"""
        # Add SHC codes from input
        shc_codes = house_waybill_info.get('shc_codes', ['ECC', 'EAP'])
        for code in shc_codes:
            handling = ET.SubElement(house_consignment, f"{{{self.ram}}}HandlingSPHInstructions")
            ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = code

        # Add SSR instructions
        ssr = ET.SubElement(house_consignment, f"{{{self.ram}}}HandlingSSRInstructions")
        ET.SubElement(ssr, f"{{{self.ram}}}Description").text = "FREIGHT COLLECT COPY INV AND P/L ATTD"

    def _add_accounting_note(self, house_consignment, house_waybill_info):
        """Add accounting note to the XML"""
        note = ET.SubElement(house_consignment, f"{{{self.ram}}}IncludedAccountingNote")
        ET.SubElement(note, f"{{{self.ram}}}ContentCode").text = "GEN"
        content_text = ("NOTIFY :KUEHNE + NAGEL LTD. 4/F, 219, NANKING EAST ROAD SECTION 3, TAIPEI "
                       "TAIWAN R.O.C. TAIPEI - TAIWAN R.O.C.")
        ET.SubElement(note, f"{{{self.ram}}}Content").text = content_text

    def _format_xml(self, root):
        """Convert ElementTree to pretty-printed XML string"""
        try:
            # Use ElementTree's built-in pretty printing
            ET.indent(ET.ElementTree(root), space="  ")

            # Convert to string and manually fix the namespace issue
            rough_string = ET.tostring(root, 'utf-8').decode('utf-8')

            # Fix namespace prefixes to use rsm and ram instead of ns0, ns1
            rough_string = rough_string.replace('ns0:', 'rsm:')
            rough_string = rough_string.replace('ns1:', 'ram:')
            rough_string = rough_string.replace('xmlns:ns0=', 'xmlns:rsm=')
            rough_string = rough_string.replace('xmlns:ns1=', 'xmlns:ram=')

            # Remove duplicate namespace declarations
            rough_string = rough_string.replace(' xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3" xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3"',
                                              ' xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3"')

            # Add XML declaration
            xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'
            return xml_declaration + rough_string
        except Exception as e:
            logger.error(f"Error formatting XML: {str(e)}", exc_info=True)
            return f"<!-- Error formatting XML: {str(e)} -->"


def generate_xfzb_from_text(input_text, config=None):
    """Generate XFZB XML from the pipe-delimited input text format"""
    try:
        # Add default values for required fields
        if not input_text or len(input_text.strip()) < 5:
            input_text = "SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP"

        generator = XFZBGenerator()
        result = generator.create_house_waybill_xml(input_text, config)
        return result
    except Exception as e:
        import traceback
        error_msg = f"Error generating XFZB XML: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"<!-- {error_msg} -->"


if __name__ == "__main__":
    # Example input in new format
    sample_input = "SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP"

    # Generate XML
    xml_output = generate_xfzb_from_text(sample_input)

    # Print or save to file
    print(xml_output)

    # Optionally save to file
    with open("generated_house_waybill.xml", "w", encoding="utf-8") as f:
        f.write(xml_output)
